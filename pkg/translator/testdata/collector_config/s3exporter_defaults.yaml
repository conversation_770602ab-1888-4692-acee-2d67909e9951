receivers:
    otlp/otlp_in:
        protocols:
            grpc:
                endpoint: ${STRAWS_COLLECTOR_POD_IP}:4317
            http:
                endpoint: ${STRAWS_COLLECTOR_POD_IP}:4318
processors:
    usage: {}
exporters:
    awss3/s3_out:
        compression: gzip
        s3uploader:
            s3_bucket: my-bucket
        sending_queue:
            batch:
                flush_timeout: 60s
                max_size: 100000
                min_size: 100000
            enabled: true
            queue_size: 1000000
            sizer: items
extensions:
    honeycomb: {}
service:
    extensions: [honeycomb]
    pipelines:
        traces:
            receivers: [otlp/otlp_in]
            processors: [usage]
            exporters: [awss3/s3_out]
